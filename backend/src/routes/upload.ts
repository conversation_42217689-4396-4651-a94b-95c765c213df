import { Router, Request, Response } from 'express';
import { as<PERSON><PERSON>and<PERSON> } from '../middleware/errorHandler';
import logger from '../config/logger';
import * as fs from 'fs';
import * as path from 'path';
import {
  validateImageData,
  generateImageFilename
} from '../utils/imageUtils';
import { ImageMimeType } from '../types/database';
import { ImageProcessingService } from '../services/ImageProcessingService';

const router = Router();

// Initialize image processing service
const imageProcessingService = new ImageProcessingService();

// Configuration for image uploads
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB (increased for better quality)

// Allowed file types
const allowedMimes: ImageMimeType[] = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

// Helper function to validate file (updated to use imageUtils)
const validateFile = (buffer: Buffer, filename: string): { isValid: boolean; error?: string; mimeType?: ImageMimeType } => {
  const validation = validateImageData(buffer, MAX_FILE_SIZE);

  if (!validation.isValid) {
    return { isValid: false, error: validation.error };
  }

  const mimeType = validation.mimeType;
  if (!mimeType || !allowedMimes.includes(mimeType)) {
    return { isValid: false, error: 'Only image files (JPEG, PNG, GIF, WebP) are allowed' };
  }

  return { isValid: true, mimeType };
};

// Helper function to create image response data
const createImageResponse = (buffer: Buffer, mimeType: ImageMimeType, filename: string) => {
  return {
    filename: generateImageFilename(filename, mimeType),
    originalName: filename,
    size: buffer.length,
    mimeType,
    data: buffer.toString('base64') // Return base64 for API response
  };
};

// Helper function to create optimized image response data
const createOptimizedImageResponse = (
  processedImage: any,
  mimeType: ImageMimeType,
  filename: string
) => {
  const baseFilename = generateImageFilename(filename, mimeType);

  return {
    original: {
      filename: baseFilename,
      originalName: filename,
      size: processedImage.metadata.originalSize,
      mimeType,
      data: processedImage.original.toString('base64')
    },
    optimized: processedImage.webp ? {
      filename: baseFilename.replace(/\.[^.]+$/, '.webp'),
      size: processedImage.metadata.compressedSize,
      mimeType: 'image/webp' as ImageMimeType,
      data: processedImage.webp.toString('base64'),
      compressionRatio: processedImage.metadata.compressionRatio
    } : null,
    sizes: {
      thumbnail: processedImage.thumbnail ? {
        filename: baseFilename.replace(/\.[^.]+$/, '_thumb.webp'),
        size: processedImage.thumbnail.length,
        data: processedImage.thumbnail.toString('base64')
      } : null,
      medium: processedImage.medium ? {
        filename: baseFilename.replace(/\.[^.]+$/, '_medium.webp'),
        size: processedImage.medium.length,
        data: processedImage.medium.toString('base64')
      } : null,
      large: processedImage.large ? {
        filename: baseFilename.replace(/\.[^.]+$/, '_large.webp'),
        size: processedImage.large.length,
        data: processedImage.large.toString('base64')
      } : null
    },
    metadata: processedImage.metadata
  };
};

// Helper function to parse multipart data
const parseMultipartData = (req: Request): Promise<{ fields: any; files: any[] }> => {
  return new Promise((resolve, reject) => {
    const boundary = req.headers['content-type']?.split('boundary=')[1];
    if (!boundary) {
      reject(new Error('No boundary found in content-type'));
      return;
    }

    const chunks: Buffer[] = [];
    
    req.on('data', (chunk) => {
      chunks.push(chunk);
    });
    
    req.on('end', () => {
      try {
        const buffer = Buffer.concat(chunks);
        const boundaryBuffer = Buffer.from(`--${boundary}`);
        const parts = [];
        
        let start = 0;
        let pos = buffer.indexOf(boundaryBuffer, start);
        
        while (pos !== -1) {
          if (start < pos) {
            parts.push(buffer.subarray(start, pos));
          }
          start = pos + boundaryBuffer.length;
          pos = buffer.indexOf(boundaryBuffer, start);
        }
        
        const fields: any = {};
        const files: any[] = [];
        
        for (const part of parts) {
          if (part.length < 4) continue;
          
          const headerEnd = part.indexOf('\r\n\r\n');
          if (headerEnd === -1) continue;
          
          const headerText = part.subarray(0, headerEnd).toString();
          const content = part.subarray(headerEnd + 4);
          
          // Remove trailing CRLF
          const actualContent = content.subarray(0, content.length - 2);
          
          const dispositionMatch = headerText.match(/Content-Disposition: form-data; name="([^"]+)"(?:; filename="([^"]+)")?/);
          if (!dispositionMatch) continue;
          
          const fieldName = dispositionMatch[1];
          const filename = dispositionMatch[2];
          
          if (filename) {
            // It's a file
            files.push({
              fieldName,
              filename,
              buffer: actualContent,
              size: actualContent.length
            });
          } else {
            // It's a regular field
            fields[fieldName] = actualContent.toString();
          }
        }
        
        resolve({ fields, files });
      } catch (error) {
        reject(error);
      }
    });
    
    req.on('error', reject);
  });
};

// Middleware to skip body parsing for upload routes
const skipBodyParser = (req: Request, res: Response, next: any) => {
  req.body = undefined;
  next();
};

/**
 * @swagger
 * /api/upload/screenshot:
 *   post:
 *     tags: [Upload]
 *     summary: Upload a screenshot file
 *     description: Upload a single screenshot image (JPEG, PNG, GIF, WebP). Maximum file size is 5MB.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Screenshot image file (max 5MB)
 *           encoding:
 *             file:
 *               contentType: image/jpeg, image/png, image/gif, image/webp
 *     responses:
 *       200:
 *         description: File uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiSuccess'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/FileUpload'
 *             example:
 *               success: true
 *               data:
 *                 filename: 'screenshot-1640995200000-123456789.jpg'
 *                 originalName: 'my-screenshot.jpg'
 *                 size: 1024768
 *                 url: '/uploads/screenshots/screenshot-1640995200000-123456789.jpg'
 *                 mimeType: 'image/jpeg'
 *               message: 'Screenshot uploaded successfully'
 *               timestamp: '2024-06-28T10:30:00.000Z'
 *       400:
 *         description: Invalid file or validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiError'
 *             examples:
 *               no_file:
 *                 summary: No file uploaded
 *                 value:
 *                   success: false
 *                   error: 'No file uploaded'
 *                   timestamp: '2024-06-28T10:30:00.000Z'
 *               invalid_type:
 *                 summary: Invalid file type
 *                 value:
 *                   success: false
 *                   error: 'Only image files (JPEG, PNG, GIF, WebP) are allowed'
 *                   timestamp: '2024-06-28T10:30:00.000Z'
 *               file_too_large:
 *                 summary: File too large
 *                 value:
 *                   success: false
 *                   error: 'File size must be less than 5MB'
 *                   timestamp: '2024-06-28T10:30:00.000Z'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
router.post('/screenshot',
  skipBodyParser,
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const { fields, files } = await parseMultipartData(req);
      
      if (files.length === 0) {
        res.status(400).json({
          success: false,
          message: 'No file uploaded'
        });
        return;
      }

      const file = files[0];
      const validation = validateFile(file.buffer, file.filename);
      
      if (!validation.isValid) {
        res.status(400).json({
          success: false,
          message: validation.error
        });
        return;
      }

      // Create image response data (no longer saving to disk)
      const imageData = createImageResponse(file.buffer, validation.mimeType!, file.filename);

      logger.info(`Processed screenshot: ${imageData.filename}, size: ${imageData.size} bytes`);

      res.success(imageData, 'Screenshot processed successfully. Use this data to save to your database.');
    } catch (error) {
      logger.error('Upload error:', error);
      res.status(500).json({
        success: false,
        message: 'Upload failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  })
);

// POST /api/upload/csv - Upload CSV file
router.post('/csv',
  skipBodyParser,
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const { fields, files } = await parseMultipartData(req);
      
      if (files.length === 0) {
        res.status(400).json({
          success: false,
          message: 'No file uploaded'
        });
        return;
      }

      const file = files[0];
      
      // Validate CSV file
      if (!file.filename.toLowerCase().endsWith('.csv')) {
        res.status(400).json({
          success: false,
          message: 'Only CSV files are allowed'
        });
        return;
      }

      if (file.size > MAX_FILE_SIZE) {
        res.status(400).json({
          success: false,
          message: 'File size must be less than 5MB'
        });
        return;
      }

      const filename = generateImageFilename(file.filename);
      const filePath = path.join(process.cwd(), 'uploads', 'csv', filename);
      
      // Ensure CSV directory exists
      const csvDir = path.dirname(filePath);
      if (!fs.existsSync(csvDir)) {
        fs.mkdirSync(csvDir, { recursive: true });
      }
      
      // Save file to disk
      fs.writeFileSync(filePath, file.buffer);

      const fileInfo = {
        filename,
        originalName: file.filename,
        size: file.size,
        path: filePath,
        url: `/uploads/csv/${filename}`
      };

      logger.info(`Uploaded CSV: ${fileInfo.filename}`);

      res.success(fileInfo, 'CSV file uploaded successfully');
    } catch (error) {
      logger.error('CSV upload error:', error);
      res.status(500).json({
        success: false,
        message: 'CSV upload failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  })
);

// GET /api/upload/files - List uploaded files (deprecated - images now stored in database)
router.get('/files',
  asyncHandler(async (req: Request, res: Response) => {
    res.success([], 'File listing is deprecated. Images are now stored directly in the database. Use the respective entity endpoints to retrieve image data.');
  })
);

// DELETE /api/upload/files/:filename - Delete uploaded file (deprecated - images now stored in database)
router.delete('/files/:filename',
  asyncHandler(async (req: Request, res: Response) => {
    res.status(410).json({
      success: false,
      message: 'File deletion is deprecated. Images are now stored directly in the database. Use the respective entity endpoints to update or delete image data.'
    });
  })
);

/**
 * @swagger
 * /api/upload/optimized:
 *   post:
 *     summary: Upload and optimize image with multiple formats and sizes
 *     description: Upload an image and automatically generate optimized versions including WebP conversion, compression, and multiple sizes (thumbnail, medium, large)
 *     tags: [Upload]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Image file to upload and optimize
 *               quality:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 100
 *                 default: 85
 *                 description: Compression quality (1-100)
 *     responses:
 *       200:
 *         description: Image uploaded and optimized successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     original:
 *                       type: object
 *                       properties:
 *                         filename:
 *                           type: string
 *                         size:
 *                           type: number
 *                         mimeType:
 *                           type: string
 *                         data:
 *                           type: string
 *                           description: Base64 encoded image data
 *                     optimized:
 *                       type: object
 *                       properties:
 *                         filename:
 *                           type: string
 *                         size:
 *                           type: number
 *                         compressionRatio:
 *                           type: number
 *                         data:
 *                           type: string
 *                           description: Base64 encoded optimized image data
 *                     sizes:
 *                       type: object
 *                       properties:
 *                         thumbnail:
 *                           type: object
 *                         medium:
 *                           type: object
 *                         large:
 *                           type: object
 *                     metadata:
 *                       type: object
 *                       properties:
 *                         originalSize:
 *                           type: number
 *                         compressedSize:
 *                           type: number
 *                         compressionRatio:
 *                           type: number
 *                         width:
 *                           type: number
 *                         height:
 *                           type: number
 *                 message:
 *                   type: string
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Invalid file or validation error
 *       500:
 *         description: Internal server error
 */
router.post('/optimized',
  asyncHandler(async (req: Request, res: Response) => {
    try {
      logger.info('Processing optimized image upload');

      const { fields, files } = await parseMultipartData(req);

      if (!files || files.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No image file provided',
          timestamp: new Date().toISOString()
        });
      }

      const file = files[0];
      const quality = fields.quality ? parseInt(fields.quality) : 85;

      // Validate quality parameter
      if (quality < 1 || quality > 100) {
        return res.status(400).json({
          success: false,
          error: 'Quality must be between 1 and 100',
          timestamp: new Date().toISOString()
        });
      }

      // Validate file
      const validation = validateFile(file.buffer, file.filename);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: validation.error,
          timestamp: new Date().toISOString()
        });
      }

      // Process image with optimization
      const processedImage = await imageProcessingService.processImage(file.buffer, {
        quality,
        format: 'webp',
        sizes: {
          thumbnail: { width: 150, height: 150 },
          medium: { width: 400, height: 400 },
          large: { width: 800, height: 800 }
        }
      });

      // Create response with all optimized versions
      const responseData = createOptimizedImageResponse(
        processedImage,
        validation.mimeType!,
        file.filename
      );

      logger.info('Optimized image upload completed', {
        originalSize: processedImage.metadata.originalSize,
        compressedSize: processedImage.metadata.compressedSize,
        compressionRatio: processedImage.metadata.compressionRatio,
        filename: file.filename
      });

      res.json({
        success: true,
        data: responseData,
        message: 'Image uploaded and optimized successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error in optimized image upload:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to process image upload',
        errorId: `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString()
      });
    }
  })
);

export default router;